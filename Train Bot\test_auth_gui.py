#!/usr/bin/env python3
"""
Test script for the authentication GUI
This will help debug authentication issues
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_auth_imports():
    """Test authentication module imports"""
    print("🧪 Testing authentication imports...")
    
    # Test auth_ui import
    try:
        from auth_ui import auth_ui
        print("✅ auth_ui imported successfully")
        print(f"   Type: {type(auth_ui)}")
        print(f"   Methods: {[method for method in dir(auth_ui) if not method.startswith('_')]}")
        auth_ui_available = True
    except ImportError as e:
        print(f"❌ auth_ui import failed: {e}")
        auth_ui_available = False
    
    # Test auth_manager import
    try:
        secret_path = os.path.join(current_dir, 'secret')
        sys.path.insert(0, secret_path)
        from auth_manager import auth_manager
        print("✅ auth_manager imported successfully")
        print(f"   Type: {type(auth_manager)}")
        print(f"   Methods: {[method for method in dir(auth_manager) if not method.startswith('_')]}")
        auth_manager_available = True
    except ImportError as e:
        print(f"❌ auth_manager import failed: {e}")
        auth_manager_available = False
    
    return auth_ui_available, auth_manager_available

def test_auth_validation():
    """Test authentication validation"""
    print("\n🔐 Testing authentication validation...")
    
    test_keys = ["demo123", "test123", "admin", "quotex2024", "wrongkey"]
    
    # Test with auth_ui if available
    try:
        from auth_ui import auth_ui
        print("\n📝 Testing with auth_ui:")
        for key in test_keys:
            try:
                if hasattr(auth_ui, 'auth_manager'):
                    result = auth_ui.auth_manager.validate_secret(key)
                    print(f"   {key}: {'✅ Valid' if result else '❌ Invalid'}")
                else:
                    print(f"   {key}: ⚠️ No auth_manager attribute")
            except Exception as e:
                print(f"   {key}: ❌ Error - {e}")
    except ImportError:
        print("⚠️ auth_ui not available for testing")
    
    # Test with auth_manager if available
    try:
        secret_path = os.path.join(current_dir, 'secret')
        sys.path.insert(0, secret_path)
        from auth_manager import auth_manager
        print("\n📝 Testing with auth_manager:")
        for key in test_keys:
            try:
                result = auth_manager.validate_secret(key)
                print(f"   {key}: {'✅ Valid' if result else '❌ Invalid'}")
            except Exception as e:
                print(f"   {key}: ❌ Error - {e}")
    except ImportError:
        print("⚠️ auth_manager not available for testing")

def test_gui_only():
    """Test just the GUI authentication dialog"""
    print("\n🖥️ Testing GUI authentication dialog...")
    
    try:
        from model_gui import AuthenticationDialog
        
        root = tk.Tk()
        root.withdraw()  # Hide the root window
        
        # Create authentication dialog
        auth_dialog = AuthenticationDialog(root)
        
        print("✅ Authentication dialog created successfully")
        print("💡 Test with these keys: demo123, test123, admin, quotex2024")
        
        # Run the dialog
        root.mainloop()
        
        if auth_dialog.result:
            print("✅ Authentication successful!")
            messagebox.showinfo("Success", "Authentication successful!")
        else:
            print("❌ Authentication failed or cancelled")
            messagebox.showwarning("Failed", "Authentication failed or cancelled")
            
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        messagebox.showerror("Error", f"GUI test failed:\n{str(e)}")

def main():
    """Main test function"""
    print("🚀 Quotex Trading Bot - Authentication Test")
    print("=" * 50)
    
    # Test imports
    auth_ui_ok, auth_manager_ok = test_auth_imports()
    
    # Test validation if modules are available
    if auth_ui_ok or auth_manager_ok:
        test_auth_validation()
    
    # Test GUI
    print("\n" + "=" * 50)
    response = input("Do you want to test the GUI authentication dialog? (y/n): ")
    if response.lower() in ['y', 'yes']:
        test_gui_only()
    
    print("\n✅ Test completed!")

if __name__ == "__main__":
    main()
