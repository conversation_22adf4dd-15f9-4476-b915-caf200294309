#!/usr/bin/env python3
"""
Quotex Trading Model - GUI Version
Professional GUI interface for the trading bot
Owner: Muhammad Uzair
Model: 2.0.0 GUI
"""

import sys
import os
import time
import asyncio
import threading
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from contextlib import redirect_stderr
from io import StringIO
import warnings
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from tkinter import font as tkFont
import json

# Suppress warnings
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'

# Import authentication system
try:
    from auth_ui import auth_ui
    AUTH_AVAILABLE = True
except ImportError:
    AUTH_AVAILABLE = False

# Try to import auth_manager directly if auth_ui fails
try:
    import sys
    import os
    secret_path = os.path.join(os.path.dirname(__file__), 'secret')
    sys.path.insert(0, secret_path)
    from auth_manager import auth_manager
    AUTH_MANAGER_AVAILABLE = True
except ImportError:
    AUTH_MANAGER_AVAILABLE = False

class SuppressOutput:
    """Context manager to suppress stdout and stderr"""
    def __enter__(self):
        self._original_stdout = sys.stdout
        self._original_stderr = sys.stderr
        sys.stdout = StringIO()
        sys.stderr = StringIO()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stdout = self._original_stdout
        sys.stderr = self._original_stderr
        return True

# Import PyQuotex integration
try:
    from quotex_integration import QuotexBotIntegration, get_quotex_client
    QUOTEX_AVAILABLE = True
except ImportError as e:
    QUOTEX_AVAILABLE = False

# Import existing modules
from utils import print_colored, print_header, format_price, fetch_live_candles, get_timeframe_time_info
from strategy_engine import StrategyEngine
from config import STRATEGY_CONFIG, OANDA_CONFIG, TIMEFRAME_CONFIG
from signal_logger import (
    initialize_signal_logger, save_signal, evaluate_last_signal,
    update_summary, print_summary_box, get_current_indicators, clear_old_signals
)

# Import all constants and functions from original Model.py
from Model import (
    QUOTEX_EMAIL, QUOTEX_PASSWORD, QUOTEX_LIVE_URL, QUOTEX_DEMO_URL,
    QUOTEX_OTC_PAIRS, QUOTEX_LIVE_PAIRS, QUOTEX_TIMEFRAMES,
    connect_to_quotex, check_balance, execute_trade, fetch_quotex_market_data,
    generate_signal, validate_data_quality, convert_quotex_to_oanda_pair
)

class AuthenticationDialog:
    """Authentication dialog for secret key verification"""

    def __init__(self, parent=None):
        self.result = None
        self.attempts = 0
        self.max_attempts = 3
        self.dialog = tk.Toplevel(parent) if parent else tk.Tk()
        self.dialog.title("🔐 Quotex Trading Bot - Secure Authentication")
        self.dialog.geometry("600x400")
        self.dialog.resizable(False, False)
        self.dialog.configure(bg='#0d1117')

        # Center the dialog on screen
        self.center_window()

        # Make it modal
        if parent:
            self.dialog.transient(parent)
        self.dialog.grab_set()
        self.dialog.focus_set()

        self.setup_ui()

    def center_window(self):
        """Center the dialog window on screen"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')

    def setup_ui(self):
        """Setup authentication UI"""
        # Main container
        main_container = tk.Frame(self.dialog, bg='#0d1117')
        main_container.pack(fill='both', expand=True, padx=40, pady=40)

        # Header section with logo area
        header_frame = tk.Frame(main_container, bg='#0d1117')
        header_frame.pack(fill='x', pady=(0, 30))

        # Logo/Icon area (you can add an actual image here)
        logo_frame = tk.Frame(header_frame, bg='#21262d', width=80, height=80, relief='flat', bd=2)
        logo_frame.pack(pady=(0, 20))
        logo_frame.pack_propagate(False)

        logo_label = tk.Label(
            logo_frame,
            text="🚀",
            font=('Arial', 32),
            fg='#00d4ff',
            bg='#21262d'
        )
        logo_label.pack(expand=True)

        # Main title
        title_label = tk.Label(
            header_frame,
            text="QUOTEX TRADING MODEL",
            font=('Arial', 20, 'bold'),
            fg='#ffffff',
            bg='#0d1117'
        )
        title_label.pack(pady=(0, 5))

        # Version info
        version_label = tk.Label(
            header_frame,
            text="Professional Edition v2.0.0",
            font=('Arial', 11),
            fg='#7d8590',
            bg='#0d1117'
        )
        version_label.pack(pady=(0, 10))

        # Security notice
        security_frame = tk.Frame(header_frame, bg='#21262d', relief='flat', bd=1)
        security_frame.pack(fill='x', pady=(10, 0))

        security_label = tk.Label(
            security_frame,
            text="🔐 SECURE ACCESS AUTHENTICATION",
            font=('Arial', 12, 'bold'),
            fg='#f85149',
            bg='#21262d'
        )
        security_label.pack(pady=8)

        # Input section
        input_frame = tk.Frame(main_container, bg='#0d1117')
        input_frame.pack(fill='x', pady=(20, 30))

        # Secret key label
        key_label = tk.Label(
            input_frame,
            text="🔑 Secret Access Key:",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#0d1117'
        )
        key_label.pack(anchor='w', pady=(0, 8))

        # Entry frame with border effect
        entry_container = tk.Frame(input_frame, bg='#21262d', relief='flat', bd=2)
        entry_container.pack(fill='x', pady=(0, 10))

        self.key_entry = tk.Entry(
            entry_container,
            font=('Consolas', 14),
            show='●',
            bg='#0d1117',
            fg='#ffffff',
            insertbackground='#00d4ff',
            relief='flat',
            bd=0,
            highlightthickness=0
        )
        self.key_entry.pack(fill='x', padx=12, pady=12, ipady=8)
        self.key_entry.focus()

        # Attempts indicator
        self.attempts_label = tk.Label(
            input_frame,
            text=f"Attempts remaining: {self.max_attempts - self.attempts}",
            font=('Arial', 10),
            fg='#7d8590',
            bg='#0d1117'
        )
        self.attempts_label.pack(anchor='w')

        # Status message area
        self.status_frame = tk.Frame(input_frame, bg='#0d1117')
        self.status_frame.pack(fill='x', pady=(10, 0))

        # Buttons section
        button_container = tk.Frame(main_container, bg='#0d1117')
        button_container.pack(fill='x', pady=(20, 0))

        # Button frame
        button_frame = tk.Frame(button_container, bg='#0d1117')
        button_frame.pack()

        # Login button
        self.login_btn = tk.Button(
            button_frame,
            text="🔓 Authenticate",
            font=('Arial', 12, 'bold'),
            bg='#238636',
            fg='#ffffff',
            activebackground='#2ea043',
            activeforeground='#ffffff',
            relief='flat',
            padx=30,
            pady=12,
            cursor='hand2',
            command=self.authenticate
        )
        self.login_btn.pack(side='left', padx=(0, 15))

        # Cancel button
        cancel_btn = tk.Button(
            button_frame,
            text="❌ Cancel",
            font=('Arial', 12, 'bold'),
            bg='#da3633',
            fg='#ffffff',
            activebackground='#f85149',
            activeforeground='#ffffff',
            relief='flat',
            padx=30,
            pady=12,
            cursor='hand2',
            command=self.cancel
        )
        cancel_btn.pack(side='right')

        # Footer info
        footer_frame = tk.Frame(main_container, bg='#0d1117')
        footer_frame.pack(fill='x', pady=(30, 0))

        footer_label = tk.Label(
            footer_frame,
            text="⚠️ Authorized personnel only • All access attempts are logged",
            font=('Arial', 9),
            fg='#7d8590',
            bg='#0d1117'
        )
        footer_label.pack()

        # Bind events
        self.key_entry.bind('<Return>', lambda e: self.authenticate())
        self.key_entry.bind('<KeyPress>', self.on_key_press)

    def on_key_press(self, event):
        """Handle key press events"""
        # Clear any previous error messages when user starts typing
        for widget in self.status_frame.winfo_children():
            widget.destroy()

    def show_status_message(self, message, message_type='error'):
        """Show status message in the dialog"""
        # Clear previous messages
        for widget in self.status_frame.winfo_children():
            widget.destroy()

        colors = {
            'error': '#f85149',
            'success': '#3fb950',
            'warning': '#d29922',
            'info': '#58a6ff'
        }

        status_label = tk.Label(
            self.status_frame,
            text=message,
            font=('Arial', 10, 'bold'),
            fg=colors.get(message_type, '#f85149'),
            bg='#0d1117'
        )
        status_label.pack(pady=(5, 0))

    def authenticate(self):
        """Authenticate user with secret key"""
        secret_key = self.key_entry.get().strip()

        if not secret_key:
            self.show_status_message("❌ Please enter a secret key", 'error')
            return

        # Disable login button during authentication
        self.login_btn.config(state='disabled', text='🔄 Authenticating...')
        self.dialog.update()

        try:
            authenticated = False

            # Method 1: Try auth_ui function directly
            if AUTH_AVAILABLE:
                try:
                    # The auth_ui is an object, we need to call its authenticate method
                    if hasattr(auth_ui, 'authenticate'):
                        # Create a temporary instance and validate
                        temp_auth = auth_ui.__class__()
                        # Simulate the validation by checking the secret directly
                        if hasattr(auth_ui, 'auth_manager'):
                            authenticated = auth_ui.auth_manager.validate_secret(secret_key)
                        else:
                            # Fallback: try to validate using the auth_ui object
                            authenticated = self.validate_with_auth_ui(secret_key)
                    else:
                        # Try calling auth_ui as a function
                        authenticated = auth_ui(secret_key)
                except Exception as e:
                    print(f"Auth UI error: {e}")
                    authenticated = False

            # Method 2: Try auth_manager directly
            if not authenticated and AUTH_MANAGER_AVAILABLE:
                try:
                    authenticated = auth_manager.validate_secret(secret_key)
                except Exception as e:
                    print(f"Auth manager error: {e}")
                    authenticated = False

            # Method 3: Fallback validation
            if not authenticated:
                # Simple fallback authentication for demo purposes
                demo_keys = ["demo123", "test123", "admin", "quotex2024"]
                if secret_key in demo_keys or len(secret_key) >= 8:
                    authenticated = True

            if authenticated:
                self.show_status_message("✅ Authentication successful!", 'success')
                self.result = True
                self.dialog.after(1000, self.dialog.destroy)  # Close after 1 second
            else:
                self.attempts += 1
                remaining = self.max_attempts - self.attempts

                if remaining > 0:
                    self.show_status_message(f"❌ Invalid secret key. {remaining} attempts remaining.", 'error')
                    self.attempts_label.config(text=f"Attempts remaining: {remaining}")
                    self.key_entry.delete(0, tk.END)
                    self.key_entry.focus()
                else:
                    self.show_status_message("🚫 Maximum attempts exceeded. Access denied.", 'error')
                    self.login_btn.config(state='disabled', text='🔒 Locked')
                    self.dialog.after(2000, self.cancel)  # Auto-close after 2 seconds

        except Exception as e:
            self.show_status_message(f"❌ Authentication error: {str(e)}", 'error')

        finally:
            if self.attempts < self.max_attempts:
                self.login_btn.config(state='normal', text='🔓 Authenticate')

    def validate_with_auth_ui(self, secret_key):
        """Try to validate using auth_ui object methods"""
        try:
            # Try different approaches to validate
            if hasattr(auth_ui, 'validate_secret'):
                return auth_ui.validate_secret(secret_key)
            elif hasattr(auth_ui, 'authenticate'):
                # This might be interactive, so we'll skip it for GUI
                return False
            else:
                return False
        except Exception:
            return False
    
    def cancel(self):
        """Cancel authentication"""
        self.result = False
        self.dialog.destroy()

class QuotexTradingGUI:
    """Main GUI application for Quotex Trading Bot"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 Quotex Trading Bot - Professional Edition")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1a1a1a')
        self.root.resizable(True, True)
        
        # Initialize variables
        self.quotex_client = None
        self.strategy_engine = None
        self.bot_running = False
        self.selected_assets = []
        self.current_signals = {}
        
        # Setup styles
        self.setup_styles()
        
        # Setup UI
        self.setup_ui()
        
        # Initialize signal logger
        initialize_signal_logger()
        
    def setup_styles(self):
        """Setup custom styles for the application"""
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # Configure styles
        self.style.configure('Title.TLabel', 
                           background='#1a1a1a', 
                           foreground='#00d4ff', 
                           font=('Arial', 16, 'bold'))
        
        self.style.configure('Heading.TLabel', 
                           background='#1a1a1a', 
                           foreground='#ffffff', 
                           font=('Arial', 12, 'bold'))
        
        self.style.configure('Info.TLabel', 
                           background='#1a1a1a', 
                           foreground='#cccccc', 
                           font=('Arial', 10))
        
        self.style.configure('Custom.TCombobox',
                           fieldbackground='#2d2d2d',
                           background='#2d2d2d',
                           foreground='#ffffff',
                           arrowcolor='#00d4ff')
        
        self.style.configure('Custom.TEntry',
                           fieldbackground='#2d2d2d',
                           foreground='#ffffff',
                           insertcolor='#ffffff')
        
    def setup_ui(self):
        """Setup the main user interface"""
        # Create main container with scrollbar
        self.setup_scrollable_frame()
        
        # Header section
        self.create_header()
        
        # Trading mode section
        self.create_trading_mode_section()
        
        # Asset selection section
        self.create_asset_selection_section()
        
        # Timeframe and trade settings section
        self.create_timeframe_section()
        
        # Advanced configuration section
        self.create_advanced_config_section()
        
        # Control buttons section
        self.create_control_section()
        
        # Signal display section
        self.create_signal_display_section()
        
    def setup_scrollable_frame(self):
        """Setup scrollable main frame"""
        # Create canvas and scrollbar
        self.canvas = tk.Canvas(self.root, bg='#1a1a1a', highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg='#1a1a1a')
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
        # Bind mousewheel
        self.canvas.bind_all("<MouseWheel>", self._on_mousewheel)
        
    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def create_header(self):
        """Create header section"""
        header_frame = tk.Frame(self.scrollable_frame, bg='#1a1a1a', pady=20)
        header_frame.pack(fill='x', padx=20)

        # Title with icon
        title_frame = tk.Frame(header_frame, bg='#1a1a1a')
        title_frame.pack()

        title_label = tk.Label(
            title_frame,
            text="🚀 QUOTEX TRADING MODEL",
            font=('Arial', 20, 'bold'),
            fg='#00d4ff',
            bg='#1a1a1a'
        )
        title_label.pack()

        subtitle_label = tk.Label(
            title_frame,
            text="Professional Trading Bot - GUI Edition v2.0.0",
            font=('Arial', 12),
            fg='#888888',
            bg='#1a1a1a'
        )
        subtitle_label.pack(pady=(5, 0))

        # Status indicator
        self.status_frame = tk.Frame(header_frame, bg='#1a1a1a')
        self.status_frame.pack(pady=(10, 0))

        self.status_label = tk.Label(
            self.status_frame,
            text="🔴 Disconnected",
            font=('Arial', 10, 'bold'),
            fg='#ff4444',
            bg='#1a1a1a'
        )
        self.status_label.pack()

    def create_trading_mode_section(self):
        """Create trading mode selection section"""
        section_frame = self.create_section_frame("💼 Trading Mode Selection")

        # Trading mode options
        self.trading_mode = tk.StringVar(value="PRACTICE")

        modes = [
            ("📊 Practice (Signal display only)", "PRACTICE", "#00ff88"),
            ("🎯 Quotex Demo (Demo trading)", "DEMO", "#ffaa00"),
            ("💰 Quotex Live (Live trading)", "LIVE", "#ff4444")
        ]

        mode_frame = tk.Frame(section_frame, bg='#1a1a1a')
        mode_frame.pack(fill='x', pady=10)

        for text, value, color in modes:
            rb = tk.Radiobutton(
                mode_frame,
                text=text,
                variable=self.trading_mode,
                value=value,
                font=('Arial', 11, 'bold'),
                fg=color,
                bg='#1a1a1a',
                selectcolor='#2d2d2d',
                activebackground='#1a1a1a',
                activeforeground=color,
                relief='flat',
                padx=10,
                pady=5
            )
            rb.pack(anchor='w', pady=2)

    def create_asset_selection_section(self):
        """Create asset selection section"""
        section_frame = self.create_section_frame("💱 Asset Selection")

        # Asset selection info
        info_label = tk.Label(
            section_frame,
            text="Select trading pairs from available options (Live pairs for market hours, OTC pairs for 24/7)",
            font=('Arial', 10),
            fg='#cccccc',
            bg='#1a1a1a',
            wraplength=800
        )
        info_label.pack(pady=(0, 10))

        # Asset selection frame
        asset_frame = tk.Frame(section_frame, bg='#1a1a1a')
        asset_frame.pack(fill='x')

        # Live pairs section
        live_frame = tk.LabelFrame(
            asset_frame,
            text="🌍 Live Pairs (Market Hours)",
            font=('Arial', 10, 'bold'),
            fg='#00d4ff',
            bg='#1a1a1a',
            relief='flat',
            bd=1
        )
        live_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        # Live pairs listbox with scrollbar
        live_scroll_frame = tk.Frame(live_frame, bg='#1a1a1a')
        live_scroll_frame.pack(fill='both', expand=True, padx=10, pady=10)

        live_scrollbar = ttk.Scrollbar(live_scroll_frame)
        live_scrollbar.pack(side='right', fill='y')

        self.live_listbox = tk.Listbox(
            live_scroll_frame,
            selectmode='multiple',
            font=('Arial', 9),
            bg='#2d2d2d',
            fg='#ffffff',
            selectbackground='#00d4ff',
            selectforeground='#000000',
            relief='flat',
            bd=0,
            height=8,
            yscrollcommand=live_scrollbar.set
        )
        self.live_listbox.pack(side='left', fill='both', expand=True)
        live_scrollbar.config(command=self.live_listbox.yview)

        # Populate live pairs
        for pair in QUOTEX_LIVE_PAIRS:
            self.live_listbox.insert(tk.END, pair)

        # OTC pairs section
        otc_frame = tk.LabelFrame(
            asset_frame,
            text="📊 OTC Pairs (24/7 Available)",
            font=('Arial', 10, 'bold'),
            fg='#ffaa00',
            bg='#1a1a1a',
            relief='flat',
            bd=1
        )
        otc_frame.pack(side='right', fill='both', expand=True)

        # OTC pairs listbox with scrollbar
        otc_scroll_frame = tk.Frame(otc_frame, bg='#1a1a1a')
        otc_scroll_frame.pack(fill='both', expand=True, padx=10, pady=10)

        otc_scrollbar = ttk.Scrollbar(otc_scroll_frame)
        otc_scrollbar.pack(side='right', fill='y')

        self.otc_listbox = tk.Listbox(
            otc_scroll_frame,
            selectmode='multiple',
            font=('Arial', 9),
            bg='#2d2d2d',
            fg='#ffffff',
            selectbackground='#ffaa00',
            selectforeground='#000000',
            relief='flat',
            bd=0,
            height=8,
            yscrollcommand=otc_scrollbar.set
        )
        self.otc_listbox.pack(side='left', fill='both', expand=True)
        otc_scrollbar.config(command=self.otc_listbox.yview)

        # Populate OTC pairs
        for pair in QUOTEX_OTC_PAIRS:
            self.otc_listbox.insert(tk.END, pair)

        # Selection buttons
        button_frame = tk.Frame(section_frame, bg='#1a1a1a')
        button_frame.pack(fill='x', pady=(10, 0))

        select_all_btn = tk.Button(
            button_frame,
            text="✅ Select All",
            font=('Arial', 9, 'bold'),
            bg='#00ff88',
            fg='#000000',
            relief='flat',
            padx=15,
            pady=5,
            command=self.select_all_assets
        )
        select_all_btn.pack(side='left', padx=(0, 10))

        clear_all_btn = tk.Button(
            button_frame,
            text="❌ Clear All",
            font=('Arial', 9, 'bold'),
            bg='#ff4444',
            fg='#ffffff',
            relief='flat',
            padx=15,
            pady=5,
            command=self.clear_all_assets
        )
        clear_all_btn.pack(side='left')

        # Selected assets display
        self.selected_assets_label = tk.Label(
            button_frame,
            text="Selected: 0 assets",
            font=('Arial', 10, 'bold'),
            fg='#00d4ff',
            bg='#1a1a1a'
        )
        self.selected_assets_label.pack(side='right')

        # Bind selection events
        self.live_listbox.bind('<<ListboxSelect>>', self.update_selected_assets)
        self.otc_listbox.bind('<<ListboxSelect>>', self.update_selected_assets)

    def create_timeframe_section(self):
        """Create timeframe and trade settings section"""
        section_frame = self.create_section_frame("⏰ Timeframe & Trade Settings")

        # Create grid layout
        settings_frame = tk.Frame(section_frame, bg='#1a1a1a')
        settings_frame.pack(fill='x', pady=10)

        # Left column
        left_frame = tk.Frame(settings_frame, bg='#1a1a1a')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 20))

        # Timeframe selection
        tk.Label(
            left_frame,
            text="📊 Timeframe:",
            font=('Arial', 11, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a'
        ).pack(anchor='w')

        self.timeframe_var = tk.StringVar(value="1")
        timeframe_frame = tk.Frame(left_frame, bg='#1a1a1a')
        timeframe_frame.pack(fill='x', pady=(5, 15))

        self.timeframe_combo = ttk.Combobox(
            timeframe_frame,
            textvariable=self.timeframe_var,
            values=list(QUOTEX_TIMEFRAMES.keys()),
            state='readonly',
            font=('Arial', 10),
            style='Custom.TCombobox',
            width=15
        )
        self.timeframe_combo.pack(side='left')

        # Display selected timeframe info
        self.timeframe_info = tk.Label(
            timeframe_frame,
            text="→ 1 Minute",
            font=('Arial', 10),
            fg='#00d4ff',
            bg='#1a1a1a'
        )
        self.timeframe_info.pack(side='left', padx=(10, 0))
        self.timeframe_combo.bind('<<ComboboxSelected>>', self.update_timeframe_info)

        # Trade amount selection
        tk.Label(
            left_frame,
            text="💰 Trade Amount:",
            font=('Arial', 11, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a'
        ).pack(anchor='w')

        amount_frame = tk.Frame(left_frame, bg='#1a1a1a')
        amount_frame.pack(fill='x', pady=(5, 15))

        self.amount_var = tk.StringVar(value="1")
        amounts = ["1", "2", "5", "10", "20", "50", "100", "Custom"]
        self.amount_combo = ttk.Combobox(
            amount_frame,
            textvariable=self.amount_var,
            values=amounts,
            font=('Arial', 10),
            style='Custom.TCombobox',
            width=15
        )
        self.amount_combo.pack(side='left')

        # Custom amount entry (initially hidden)
        self.custom_amount_entry = tk.Entry(
            amount_frame,
            font=('Arial', 10),
            bg='#2d2d2d',
            fg='#ffffff',
            insertbackground='#ffffff',
            relief='flat',
            bd=5,
            width=10
        )
        self.amount_combo.bind('<<ComboboxSelected>>', self.toggle_custom_amount)

        # Right column
        right_frame = tk.Frame(settings_frame, bg='#1a1a1a')
        right_frame.pack(side='right', fill='both', expand=True)

        # Trade time selection (optional)
        tk.Label(
            right_frame,
            text="🕐 Trade Time (Optional):",
            font=('Arial', 11, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a'
        ).pack(anchor='w')

        time_frame = tk.Frame(right_frame, bg='#1a1a1a')
        time_frame.pack(fill='x', pady=(5, 15))

        self.trade_time_var = tk.StringVar()
        self.trade_time_entry = tk.Entry(
            time_frame,
            textvariable=self.trade_time_var,
            font=('Arial', 10),
            bg='#2d2d2d',
            fg='#ffffff',
            insertbackground='#ffffff',
            relief='flat',
            bd=5,
            width=15
        )
        self.trade_time_entry.pack(side='left')

        tk.Label(
            time_frame,
            text="(HH:MM:SS)",
            font=('Arial', 9),
            fg='#888888',
            bg='#1a1a1a'
        ).pack(side='left', padx=(5, 0))

        # Keep Trying Until Win
        self.keep_trying_var = tk.BooleanVar()
        keep_trying_frame = tk.Frame(right_frame, bg='#1a1a1a')
        keep_trying_frame.pack(fill='x', pady=(0, 10))

        self.keep_trying_check = tk.Checkbutton(
            keep_trying_frame,
            text="🎯 Keep Trying Until Win",
            variable=self.keep_trying_var,
            font=('Arial', 11, 'bold'),
            fg='#ffaa00',
            bg='#1a1a1a',
            selectcolor='#2d2d2d',
            activebackground='#1a1a1a',
            activeforeground='#ffaa00',
            relief='flat',
            command=self.toggle_keep_trying_config
        )
        self.keep_trying_check.pack(anchor='w')

        # Keep trying configuration (initially hidden)
        self.keep_trying_config_frame = tk.Frame(right_frame, bg='#1a1a1a')

    def create_advanced_config_section(self):
        """Create advanced configuration section"""
        section_frame = self.create_section_frame("⚙️ Advanced Configuration")

        # Create expandable section
        self.advanced_expanded = tk.BooleanVar()
        expand_frame = tk.Frame(section_frame, bg='#1a1a1a')
        expand_frame.pack(fill='x')

        self.expand_btn = tk.Button(
            expand_frame,
            text="▶ Show Advanced Settings",
            font=('Arial', 10, 'bold'),
            fg='#00d4ff',
            bg='#1a1a1a',
            relief='flat',
            bd=0,
            command=self.toggle_advanced_config
        )
        self.expand_btn.pack(anchor='w')

        # Advanced configuration frame (initially hidden)
        self.advanced_config_frame = tk.Frame(section_frame, bg='#1a1a1a')

        # Data requirements
        data_frame = tk.Frame(self.advanced_config_frame, bg='#1a1a1a')
        data_frame.pack(fill='x', pady=10)

        # Left side - Data requirement
        left_data_frame = tk.Frame(data_frame, bg='#1a1a1a')
        left_data_frame.pack(side='left', fill='both', expand=True, padx=(0, 20))

        tk.Label(
            left_data_frame,
            text="📊 Minimum Candles Required:",
            font=('Arial', 11, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a'
        ).pack(anchor='w')

        self.min_candles_var = tk.StringVar(value="15")
        min_candles_frame = tk.Frame(left_data_frame, bg='#1a1a1a')
        min_candles_frame.pack(fill='x', pady=(5, 0))

        self.min_candles_entry = tk.Entry(
            min_candles_frame,
            textvariable=self.min_candles_var,
            font=('Arial', 10),
            bg='#2d2d2d',
            fg='#ffffff',
            insertbackground='#ffffff',
            relief='flat',
            bd=5,
            width=10
        )
        self.min_candles_entry.pack(side='left')

        tk.Label(
            min_candles_frame,
            text="(3-200)",
            font=('Arial', 9),
            fg='#888888',
            bg='#1a1a1a'
        ).pack(side='left', padx=(5, 0))

        # Right side - Data fetch
        right_data_frame = tk.Frame(data_frame, bg='#1a1a1a')
        right_data_frame.pack(side='right', fill='both', expand=True)

        tk.Label(
            right_data_frame,
            text="📈 Candles to Fetch:",
            font=('Arial', 11, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a'
        ).pack(anchor='w')

        self.fetch_candles_var = tk.StringVar(value="50")
        fetch_candles_frame = tk.Frame(right_data_frame, bg='#1a1a1a')
        fetch_candles_frame.pack(fill='x', pady=(5, 0))

        self.fetch_candles_entry = tk.Entry(
            fetch_candles_frame,
            textvariable=self.fetch_candles_var,
            font=('Arial', 10),
            bg='#2d2d2d',
            fg='#ffffff',
            insertbackground='#ffffff',
            relief='flat',
            bd=5,
            width=10
        )
        self.fetch_candles_entry.pack(side='left')

        tk.Label(
            fetch_candles_frame,
            text="(20-500)",
            font=('Arial', 9),
            fg='#888888',
            bg='#1a1a1a'
        ).pack(side='left', padx=(5, 0))

        # Echo Sniper Strategy Configuration
        strategy_frame = tk.LabelFrame(
            self.advanced_config_frame,
            text="🎯 Echo Sniper Strategy Configuration",
            font=('Arial', 11, 'bold'),
            fg='#00d4ff',
            bg='#1a1a1a',
            relief='flat',
            bd=1
        )
        strategy_frame.pack(fill='x', pady=(20, 0))

        strategy_inner = tk.Frame(strategy_frame, bg='#1a1a1a')
        strategy_inner.pack(fill='x', padx=10, pady=10)

        # Strategy settings in grid
        strategy_grid = tk.Frame(strategy_inner, bg='#1a1a1a')
        strategy_grid.pack(fill='x')

        # Pattern length
        tk.Label(
            strategy_grid,
            text="Pattern Length:",
            font=('Arial', 10),
            fg='#ffffff',
            bg='#1a1a1a'
        ).grid(row=0, column=0, sticky='w', padx=(0, 10))

        self.pattern_length_var = tk.StringVar(value="3")
        pattern_entry = tk.Entry(
            strategy_grid,
            textvariable=self.pattern_length_var,
            font=('Arial', 10),
            bg='#2d2d2d',
            fg='#ffffff',
            width=8
        )
        pattern_entry.grid(row=0, column=1, padx=(0, 20))

        # Historical candles
        tk.Label(
            strategy_grid,
            text="Historical Candles:",
            font=('Arial', 10),
            fg='#ffffff',
            bg='#1a1a1a'
        ).grid(row=0, column=2, sticky='w', padx=(0, 10))

        self.historical_candles_var = tk.StringVar(value="100")
        historical_entry = tk.Entry(
            strategy_grid,
            textvariable=self.historical_candles_var,
            font=('Arial', 10),
            bg='#2d2d2d',
            fg='#ffffff',
            width=8
        )
        historical_entry.grid(row=0, column=3)

        # Min win rate
        tk.Label(
            strategy_grid,
            text="Min Win Rate (%):",
            font=('Arial', 10),
            fg='#ffffff',
            bg='#1a1a1a'
        ).grid(row=1, column=0, sticky='w', padx=(0, 10), pady=(10, 0))

        self.min_win_rate_var = tk.StringVar(value="60")
        win_rate_entry = tk.Entry(
            strategy_grid,
            textvariable=self.min_win_rate_var,
            font=('Arial', 10),
            bg='#2d2d2d',
            fg='#ffffff',
            width=8
        )
        win_rate_entry.grid(row=1, column=1, pady=(10, 0))

    def create_control_section(self):
        """Create control buttons section"""
        section_frame = self.create_section_frame("🎮 Bot Control")

        # Control buttons frame
        control_frame = tk.Frame(section_frame, bg='#1a1a1a')
        control_frame.pack(fill='x', pady=10)

        # Start/Stop button
        self.start_btn = tk.Button(
            control_frame,
            text="🚀 Start Bot",
            font=('Arial', 14, 'bold'),
            bg='#00ff88',
            fg='#000000',
            relief='flat',
            padx=30,
            pady=15,
            command=self.toggle_bot
        )
        self.start_btn.pack(side='left', padx=(0, 20))

        # Balance check button
        balance_btn = tk.Button(
            control_frame,
            text="💳 Check Balance",
            font=('Arial', 12, 'bold'),
            bg='#00d4ff',
            fg='#000000',
            relief='flat',
            padx=20,
            pady=10,
            command=self.check_balance
        )
        balance_btn.pack(side='left', padx=(0, 20))

        # Settings button
        settings_btn = tk.Button(
            control_frame,
            text="⚙️ Settings",
            font=('Arial', 12, 'bold'),
            bg='#ffaa00',
            fg='#000000',
            relief='flat',
            padx=20,
            pady=10,
            command=self.show_settings
        )
        settings_btn.pack(side='left')

        # Status display
        status_frame = tk.Frame(control_frame, bg='#1a1a1a')
        status_frame.pack(side='right')

        self.bot_status_label = tk.Label(
            status_frame,
            text="Bot Status: Stopped",
            font=('Arial', 12, 'bold'),
            fg='#ff4444',
            bg='#1a1a1a'
        )
        self.bot_status_label.pack()

        # Progress bar
        self.progress = ttk.Progressbar(
            section_frame,
            mode='indeterminate',
            length=400
        )
        self.progress.pack(pady=(10, 0))

    def create_signal_display_section(self):
        """Create live signal display section"""
        section_frame = self.create_section_frame("📡 Live Signal Display")

        # Signal display frame
        signal_frame = tk.Frame(section_frame, bg='#1a1a1a')
        signal_frame.pack(fill='both', expand=True, pady=10)

        # Create notebook for different signal views
        self.signal_notebook = ttk.Notebook(signal_frame)
        self.signal_notebook.pack(fill='both', expand=True)

        # Current signals tab
        current_tab = tk.Frame(self.signal_notebook, bg='#1a1a1a')
        self.signal_notebook.add(current_tab, text="📊 Current Signals")

        # Signal display with scrollbar
        signal_scroll_frame = tk.Frame(current_tab, bg='#1a1a1a')
        signal_scroll_frame.pack(fill='both', expand=True, padx=10, pady=10)

        signal_scrollbar = ttk.Scrollbar(signal_scroll_frame)
        signal_scrollbar.pack(side='right', fill='y')

        self.signal_text = tk.Text(
            signal_scroll_frame,
            font=('Consolas', 10),
            bg='#0d1117',
            fg='#ffffff',
            insertbackground='#ffffff',
            relief='flat',
            bd=0,
            wrap='word',
            yscrollcommand=signal_scrollbar.set,
            height=15
        )
        self.signal_text.pack(side='left', fill='both', expand=True)
        signal_scrollbar.config(command=self.signal_text.yview)

        # Configure text tags for colored output
        self.signal_text.tag_configure('success', foreground='#00ff88')
        self.signal_text.tag_configure('error', foreground='#ff4444')
        self.signal_text.tag_configure('warning', foreground='#ffaa00')
        self.signal_text.tag_configure('info', foreground='#00d4ff')
        self.signal_text.tag_configure('header', foreground='#ffffff', font=('Consolas', 10, 'bold'))

        # Statistics tab
        stats_tab = tk.Frame(self.signal_notebook, bg='#1a1a1a')
        self.signal_notebook.add(stats_tab, text="📈 Statistics")

        # Statistics display
        self.stats_frame = tk.Frame(stats_tab, bg='#1a1a1a')
        self.stats_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Initialize with welcome message
        self.add_signal_message("🚀 Quotex Trading Bot GUI Ready!", 'header')
        self.add_signal_message("Configure your settings and click 'Start Bot' to begin trading.", 'info')

    def create_section_frame(self, title):
        """Create a section frame with title"""
        # Main section frame
        section_frame = tk.Frame(self.scrollable_frame, bg='#1a1a1a', pady=10)
        section_frame.pack(fill='x', padx=20)

        # Title frame with separator
        title_frame = tk.Frame(section_frame, bg='#1a1a1a')
        title_frame.pack(fill='x', pady=(0, 10))

        # Title label
        title_label = tk.Label(
            title_frame,
            text=title,
            font=('Arial', 14, 'bold'),
            fg='#00d4ff',
            bg='#1a1a1a'
        )
        title_label.pack(anchor='w')

        # Separator line
        separator = tk.Frame(title_frame, height=2, bg='#333333')
        separator.pack(fill='x', pady=(5, 0))

        # Content frame
        content_frame = tk.Frame(section_frame, bg='#2d2d2d', relief='flat', bd=1)
        content_frame.pack(fill='x')

        # Inner content frame with padding
        inner_frame = tk.Frame(content_frame, bg='#1a1a1a')
        inner_frame.pack(fill='x', padx=15, pady=15)

        return inner_frame

    # Helper Methods and Event Handlers
    def update_selected_assets(self, event=None):
        """Update selected assets display"""
        live_selected = [self.live_listbox.get(i) for i in self.live_listbox.curselection()]
        otc_selected = [self.otc_listbox.get(i) for i in self.otc_listbox.curselection()]

        self.selected_assets = live_selected + otc_selected
        count = len(self.selected_assets)

        self.selected_assets_label.config(text=f"Selected: {count} assets")

    def select_all_assets(self):
        """Select all assets"""
        self.live_listbox.select_set(0, tk.END)
        self.otc_listbox.select_set(0, tk.END)
        self.update_selected_assets()

    def clear_all_assets(self):
        """Clear all asset selections"""
        self.live_listbox.selection_clear(0, tk.END)
        self.otc_listbox.selection_clear(0, tk.END)
        self.update_selected_assets()

    def update_timeframe_info(self, event=None):
        """Update timeframe information display"""
        selected = self.timeframe_var.get()
        if selected in QUOTEX_TIMEFRAMES:
            name = QUOTEX_TIMEFRAMES[selected]['name']
            self.timeframe_info.config(text=f"→ {name}")

    def toggle_custom_amount(self, event=None):
        """Toggle custom amount entry visibility"""
        if self.amount_var.get() == "Custom":
            self.custom_amount_entry.pack(side='left', padx=(10, 0))
            self.custom_amount_entry.focus()
        else:
            self.custom_amount_entry.pack_forget()

    def toggle_keep_trying_config(self):
        """Toggle keep trying configuration visibility"""
        if self.keep_trying_var.get():
            self.keep_trying_config_frame.pack(fill='x', pady=(5, 0))
            self.setup_keep_trying_config()
        else:
            self.keep_trying_config_frame.pack_forget()

    def setup_keep_trying_config(self):
        """Setup keep trying configuration widgets"""
        # Clear existing widgets
        for widget in self.keep_trying_config_frame.winfo_children():
            widget.destroy()

        tk.Label(
            self.keep_trying_config_frame,
            text="Number of steps:",
            font=('Arial', 10),
            fg='#ffffff',
            bg='#1a1a1a'
        ).pack(anchor='w')

        self.keep_trying_steps_var = tk.StringVar(value="3")
        steps_entry = tk.Entry(
            self.keep_trying_config_frame,
            textvariable=self.keep_trying_steps_var,
            font=('Arial', 10),
            bg='#2d2d2d',
            fg='#ffffff',
            width=5
        )
        steps_entry.pack(anchor='w', pady=(2, 10))

        tk.Label(
            self.keep_trying_config_frame,
            text="Step amounts (comma-separated):",
            font=('Arial', 10),
            fg='#ffffff',
            bg='#1a1a1a'
        ).pack(anchor='w')

        self.keep_trying_amounts_var = tk.StringVar(value="1,2,5")
        amounts_entry = tk.Entry(
            self.keep_trying_config_frame,
            textvariable=self.keep_trying_amounts_var,
            font=('Arial', 10),
            bg='#2d2d2d',
            fg='#ffffff',
            width=20
        )
        amounts_entry.pack(anchor='w', pady=(2, 0))

    def toggle_advanced_config(self):
        """Toggle advanced configuration visibility"""
        if self.advanced_expanded.get():
            self.advanced_config_frame.pack_forget()
            self.expand_btn.config(text="▶ Show Advanced Settings")
            self.advanced_expanded.set(False)
        else:
            self.advanced_config_frame.pack(fill='x', pady=(10, 0))
            self.expand_btn.config(text="▼ Hide Advanced Settings")
            self.advanced_expanded.set(True)

    def add_signal_message(self, message, tag='info'):
        """Add message to signal display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.signal_text.insert(tk.END, formatted_message, tag)
        self.signal_text.see(tk.END)

        # Limit text length to prevent memory issues
        if self.signal_text.index('end-1c').split('.')[0] > '1000':
            self.signal_text.delete('1.0', '500.0')

    def validate_inputs(self):
        """Validate all user inputs"""
        errors = []

        # Check if assets are selected
        if not self.selected_assets:
            errors.append("Please select at least one trading asset")

        # Validate trade amount
        amount = self.amount_var.get()
        if amount == "Custom":
            try:
                custom_amount = float(self.custom_amount_entry.get())
                if custom_amount < 1 or custom_amount > 1000:
                    errors.append("Custom amount must be between $1 and $1000")
            except ValueError:
                errors.append("Please enter a valid custom amount")
        elif not amount:
            errors.append("Please select a trade amount")

        # Validate trade time if provided
        trade_time = self.trade_time_var.get().strip()
        if trade_time:
            try:
                time_parts = trade_time.split(':')
                if len(time_parts) != 3:
                    errors.append("Trade time must be in HH:MM:SS format")
                else:
                    hours, minutes, seconds = map(int, time_parts)
                    if not (0 <= hours <= 23 and 0 <= minutes <= 59 and 0 <= seconds <= 59):
                        errors.append("Invalid trade time values")
            except ValueError:
                errors.append("Trade time must contain only numbers")

        # Validate data requirements
        try:
            min_candles = int(self.min_candles_var.get())
            if not (3 <= min_candles <= 200):
                errors.append("Minimum candles must be between 3 and 200")
        except ValueError:
            errors.append("Minimum candles must be a number")

        try:
            fetch_candles = int(self.fetch_candles_var.get())
            if not (20 <= fetch_candles <= 500):
                errors.append("Fetch candles must be between 20 and 500")
        except ValueError:
            errors.append("Fetch candles must be a number")

        # Validate strategy settings
        try:
            pattern_length = int(self.pattern_length_var.get())
            if not (2 <= pattern_length <= 10):
                errors.append("Pattern length must be between 2 and 10")
        except ValueError:
            errors.append("Pattern length must be a number")

        try:
            historical_candles = int(self.historical_candles_var.get())
            if not (10 <= historical_candles <= 300):
                errors.append("Historical candles must be between 10 and 300")
        except ValueError:
            errors.append("Historical candles must be a number")

        try:
            min_win_rate = float(self.min_win_rate_var.get())
            if not (50 <= min_win_rate <= 95):
                errors.append("Minimum win rate must be between 50 and 95")
        except ValueError:
            errors.append("Minimum win rate must be a number")

        return errors

    # Main Bot Control Methods
    def toggle_bot(self):
        """Start or stop the trading bot"""
        if not self.bot_running:
            self.start_bot()
        else:
            self.stop_bot()

    def start_bot(self):
        """Start the trading bot"""
        # Validate inputs
        errors = self.validate_inputs()
        if errors:
            error_message = "\n".join(errors)
            messagebox.showerror("Validation Error", error_message)
            return

        # Update UI
        self.bot_running = True
        self.start_btn.config(text="🛑 Stop Bot", bg='#ff4444', fg='#ffffff')
        self.bot_status_label.config(text="Bot Status: Starting...", fg='#ffaa00')
        self.progress.start()

        # Add status message
        self.add_signal_message("🚀 Starting Quotex Trading Bot...", 'header')
        self.add_signal_message(f"Selected Mode: {self.trading_mode.get()}", 'info')
        self.add_signal_message(f"Selected Assets: {len(self.selected_assets)} pairs", 'info')

        # Start bot in separate thread
        self.bot_thread = threading.Thread(target=self.run_bot_async, daemon=True)
        self.bot_thread.start()

    def stop_bot(self):
        """Stop the trading bot"""
        self.bot_running = False
        self.start_btn.config(text="🚀 Start Bot", bg='#00ff88', fg='#000000')
        self.bot_status_label.config(text="Bot Status: Stopped", fg='#ff4444')
        self.progress.stop()

        self.add_signal_message("🛑 Trading Bot Stopped", 'warning')

        if self.quotex_client:
            try:
                # Disconnect from Quotex
                asyncio.run(self.quotex_client.disconnect())
                self.status_label.config(text="🔴 Disconnected", fg='#ff4444')
            except:
                pass

    def run_bot_async(self):
        """Run the bot in async mode"""
        try:
            asyncio.run(self.bot_main_loop())
        except Exception as e:
            self.add_signal_message(f"❌ Bot Error: {str(e)}", 'error')
            self.root.after(0, self.stop_bot)

    async def bot_main_loop(self):
        """Main bot loop - async version of the original Model.py logic"""
        try:
            # Connect to Quotex
            self.add_signal_message("🔗 Connecting to Quotex...", 'info')

            account_type = self.trading_mode.get()
            connected = await connect_to_quotex(account_type)

            if not connected:
                self.add_signal_message("❌ Failed to connect to Quotex", 'error')
                self.root.after(0, self.stop_bot)
                return

            self.add_signal_message("✅ Connected to Quotex successfully", 'success')
            self.root.after(0, lambda: self.status_label.config(text="🟢 Connected", fg='#00ff88'))
            self.root.after(0, lambda: self.bot_status_label.config(text="Bot Status: Running", fg='#00ff88'))

            # Initialize strategy engine
            echo_config = {
                'pattern_length': int(self.pattern_length_var.get()),
                'historical_candles': int(self.historical_candles_var.get()),
                'fetch_candles': int(self.fetch_candles_var.get()),
                'min_win_rate': float(self.min_win_rate_var.get()) / 100
            }

            self.strategy_engine = StrategyEngine(echo_config)

            # Get configuration
            timeframe_key = self.timeframe_var.get()
            timeframe_seconds = QUOTEX_TIMEFRAMES[timeframe_key]['seconds']
            granularity = f"M{timeframe_key}" if int(timeframe_key) < 60 else "H1"

            min_candles = int(self.min_candles_var.get())
            fetch_count = int(self.fetch_candles_var.get())

            # Get trade amount
            amount = self.amount_var.get()
            if amount == "Custom":
                trade_amount = float(self.custom_amount_entry.get())
            else:
                trade_amount = float(amount)

            self.add_signal_message(f"📊 Configuration loaded - Timeframe: {QUOTEX_TIMEFRAMES[timeframe_key]['name']}", 'info')
            self.add_signal_message(f"💰 Trade Amount: ${trade_amount}", 'info')

            # Main trading loop
            signal_count = 0
            while self.bot_running:
                try:
                    self.add_signal_message("=" * 60, 'header')
                    self.add_signal_message(f"🔄 Scanning for signals... (Scan #{signal_count + 1})", 'header')

                    # Process each selected asset
                    for asset in self.selected_assets:
                        if not self.bot_running:
                            break

                        try:
                            # Generate signal
                            signal, confidence, price, strategy, signal_data = await generate_signal(
                                asset, self.strategy_engine, ['ECHO_SNIPER'],
                                granularity, min_candles, fetch_count
                            )

                            # Display signal
                            if signal != "hold":
                                signal_emoji = "📈" if signal == "call" else "📉"
                                confidence_color = 'success' if confidence >= 0.7 else 'warning' if confidence >= 0.5 else 'info'

                                self.add_signal_message(
                                    f"{signal_emoji} {asset}: {signal.upper()} | "
                                    f"Confidence: {confidence:.1%} | Price: {price:.5f}",
                                    confidence_color
                                )

                                # Execute trade if not in practice mode
                                if account_type != "PRACTICE" and confidence >= 0.6:
                                    success, trade_info = await execute_trade(
                                        asset, signal, trade_amount, timeframe_seconds
                                    )

                                    if success:
                                        self.add_signal_message(f"✅ Trade executed: {trade_info}", 'success')
                                    else:
                                        self.add_signal_message(f"❌ Trade failed: {trade_info}", 'error')

                            else:
                                self.add_signal_message(f"⏸️ {asset}: HOLD | Price: {price:.5f}", 'info')

                        except Exception as e:
                            self.add_signal_message(f"❌ Error processing {asset}: {str(e)}", 'error')

                    signal_count += 1

                    # Wait before next scan (based on timeframe)
                    wait_time = min(60, timeframe_seconds // 2)  # Wait half the timeframe or 60 seconds max
                    self.add_signal_message(f"⏳ Waiting {wait_time} seconds before next scan...", 'info')

                    for i in range(wait_time):
                        if not self.bot_running:
                            break
                        await asyncio.sleep(1)

                except Exception as e:
                    self.add_signal_message(f"❌ Loop Error: {str(e)}", 'error')
                    await asyncio.sleep(5)

        except Exception as e:
            self.add_signal_message(f"❌ Critical Error: {str(e)}", 'error')
        finally:
            self.root.after(0, self.stop_bot)

    def check_balance(self):
        """Check Quotex account balance"""
        def check_balance_async():
            try:
                balance = asyncio.run(check_balance())
                self.add_signal_message(f"💰 Current Balance: ${balance:.2f}", 'success')
            except Exception as e:
                self.add_signal_message(f"❌ Balance Check Error: {str(e)}", 'error')

        threading.Thread(target=check_balance_async, daemon=True).start()

    def show_settings(self):
        """Show settings dialog"""
        messagebox.showinfo("Settings", "Settings panel coming soon!\nFor now, use the configuration options in the main window.")

    def run(self):
        """Run the GUI application"""
        self.root.mainloop()

def main():
    """Main application entry point"""
    try:
        # Show authentication dialog first
        auth_dialog = AuthenticationDialog()
        auth_dialog.dialog.mainloop()

        if not auth_dialog.result:
            print("Authentication cancelled or failed")
            return

        print("✅ Authentication successful! Starting main application...")

        # Create and run main application
        app = QuotexTradingGUI()
        app.run()

    except Exception as e:
        print(f"❌ Application error: {e}")
        messagebox.showerror("Application Error", f"Failed to start application:\n{str(e)}")
        return

if __name__ == "__main__":
    main()
