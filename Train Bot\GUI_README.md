# 🚀 Quotex Trading Bot - GUI Version

## Overview
This is a professional GUI version of the Quotex Trading Bot, converted from the original terminal-based `Model.py` script. The GUI provides an intuitive, user-friendly interface while maintaining all the original functionality.

## Features

### 🔐 Authentication
- Secure login with secret key verification
- Professional authentication dialog

### 💼 Trading Modes
- **Practice Mode**: Signal display only (no actual trades)
- **Demo Mode**: Demo trading with virtual money
- **Live Mode**: Real money trading

### 💱 Asset Selection
- **Live Pairs**: Market hours trading (Forex majors)
- **OTC Pairs**: 24/7 available pairs (Forex, Commodities, Stocks)
- Multi-selection support
- Select All / Clear All functionality

### ⏰ Timeframe & Settings
- Multiple timeframes: 1min, 2min, 5min, 10min, 15min, 30min, 1hour
- Customizable trade amounts ($1-$1000)
- Optional trade time setting (HH:MM:SS)
- Keep Trying Until Win feature

### ⚙️ Advanced Configuration
- **Data Requirements**: Minimum candles needed (3-200)
- **Data Fetch**: Number of candles to fetch (20-500)
- **Echo Sniper Strategy**: Pattern-based trading configuration
  - Pattern Length (2-10 candles)
  - Historical Analysis (10-300 candles)
  - Minimum Win Rate (50-95%)

### 📡 Live Signal Display
- Real-time signal updates
- Color-coded messages (Success, Warning, Error, Info)
- Scrollable signal history
- Statistics tracking

## How to Use

### 1. Launch the Application
```bash
python run_gui.py
```
or
```bash
python model_gui.py
```

### 2. Authentication
- Enter your secret key in the authentication dialog
- Click "Login" to proceed

### 3. Configure Settings
1. **Select Trading Mode**: Choose Practice, Demo, or Live
2. **Select Assets**: Choose from Live or OTC pairs
3. **Set Timeframe**: Select your preferred trading timeframe
4. **Configure Trade Amount**: Set your trade amount
5. **Advanced Settings** (Optional): Expand for detailed configuration

### 4. Start Trading
- Click "🚀 Start Bot" to begin
- Monitor signals in the Live Signal Display area
- Use "💳 Check Balance" to view account balance
- Click "🛑 Stop Bot" to stop trading

## GUI Layout

```
┌─────────────────────────────────────────────────────────────┐
│ 🚀 QUOTEX TRADING MODEL - Professional Edition             │
├─────────────────────────────────────────────────────────────┤
│ 💼 Trading Mode Selection                                   │
│ ○ Practice  ○ Demo  ○ Live                                 │
├─────────────────────────────────────────────────────────────┤
│ 💱 Asset Selection                                          │
│ ┌─Live Pairs─────┐  ┌─OTC Pairs──────┐                    │
│ │ EURUSD         │  │ EURUSD_otc     │                    │
│ │ GBPUSD         │  │ GBPUSD_otc     │                    │
│ │ ...            │  │ ...            │                    │
│ └────────────────┘  └────────────────┘                    │
├─────────────────────────────────────────────────────────────┤
│ ⏰ Timeframe & Trade Settings                               │
│ Timeframe: [1min ▼]  Amount: [$1 ▼]  Time: [HH:MM:SS]    │
├─────────────────────────────────────────────────────────────┤
│ ⚙️ Advanced Configuration                                   │
│ ▶ Show Advanced Settings                                   │
├─────────────────────────────────────────────────────────────┤
│ 🎮 Bot Control                                              │
│ [🚀 Start Bot] [💳 Check Balance] [⚙️ Settings]           │
├─────────────────────────────────────────────────────────────┤
│ 📡 Live Signal Display                                      │
│ ┌─Current Signals─┐ ┌─Statistics─┐                        │
│ │ [12:34:56] 📈   │ │ Win Rate:  │                        │
│ │ EURUSD: CALL    │ │ 65.2%      │                        │
│ │ Confidence: 75% │ │ Total: 23  │                        │
│ │ ...             │ │ ...        │                        │
│ └─────────────────┘ └────────────┘                        │
└─────────────────────────────────────────────────────────────┘
```

## Color Coding

- 🟢 **Green**: Success messages, profitable signals
- 🟡 **Yellow**: Warning messages, medium confidence
- 🔴 **Red**: Error messages, losses
- 🔵 **Blue**: Information messages, system status
- ⚪ **White**: Headers and general text

## Requirements

- Python 3.7+
- tkinter (usually included with Python)
- All original dependencies from Model.py:
  - pandas
  - numpy
  - requests
  - asyncio
  - PyQuotex integration
  - Strategy engine
  - Signal logger

## Differences from Terminal Version

### Advantages of GUI Version:
- ✅ User-friendly visual interface
- ✅ Real-time signal display
- ✅ No terminal commands needed
- ✅ Professional appearance
- ✅ Multi-selection for assets
- ✅ Expandable configuration sections
- ✅ Color-coded status messages
- ✅ Progress indicators

### Maintained Features:
- ✅ Same authentication system
- ✅ Identical trading logic
- ✅ Same signal generation algorithm
- ✅ All configuration options
- ✅ Real market data integration
- ✅ Echo Sniper strategy
- ✅ Keep Trying Until Win feature

## Troubleshooting

### Common Issues:

1. **Authentication Failed**
   - Check your secret key
   - Ensure auth_ui module is available

2. **Connection Issues**
   - Verify internet connection
   - Check Quotex credentials
   - Ensure PyQuotex integration is working

3. **No Signals Generated**
   - Check asset selection
   - Verify market hours for Live pairs
   - Ensure sufficient historical data

4. **GUI Not Responding**
   - Bot operations run in background threads
   - Check signal display for error messages
   - Restart application if needed

## Support

For issues or questions:
1. Check the signal display for error messages
2. Verify all configuration settings
3. Ensure all dependencies are installed
4. Check the original Model.py for reference

---

**Note**: This GUI version maintains the exact same trading logic as the original Model.py file. It simply provides a more user-friendly interface for configuration and monitoring.
