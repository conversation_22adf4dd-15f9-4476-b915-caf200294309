#!/usr/bin/env python3
"""
Quotex Trading Bot GUI Launcher
Simple launcher script for the GUI version
"""

import sys
import os

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from model_gui import main
    
    if __name__ == "__main__":
        print("🚀 Starting Quotex Trading Bot GUI...")
        main()
        
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Please make sure all required modules are installed.")
    input("Press Enter to exit...")
    
except Exception as e:
    print(f"❌ Error: {e}")
    input("Press Enter to exit...")
