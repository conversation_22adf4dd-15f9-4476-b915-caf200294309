#!/usr/bin/env python3
"""
Quotex Trading Bot GUI Launcher
Simple launcher script for the GUI version
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []

    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")

    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")

    try:
        import requests
    except ImportError:
        missing_deps.append("requests")

    return missing_deps

def show_welcome():
    """Show welcome message"""
    print("🚀 QUOTEX TRADING BOT - GUI VERSION")
    print("=" * 50)
    print("Professional Trading Bot Interface")
    print("Version: 2.0.0 GUI Edition")
    print("=" * 50)

def main():
    """Main launcher function"""
    show_welcome()

    # Check dependencies
    print("🔍 Checking dependencies...")
    missing = check_dependencies()
    if missing:
        print(f"❌ Missing dependencies: {', '.join(missing)}")
        print("Please install missing packages using:")
        print(f"pip install {' '.join(missing)}")
        input("Press Enter to exit...")
        return

    print("✅ All dependencies found")

    try:
        print("📦 Loading GUI modules...")
        from model_gui import main as gui_main

        print("🎨 Starting GUI application...")
        print("\n💡 AUTHENTICATION TIPS:")
        print("   • Try these demo keys: demo123, test123, admin, quotex2024")
        print("   • Or any key with 8+ characters")
        print("   • The authentication dialog will appear shortly...")
        print("\n🚀 Launching GUI...")

        gui_main()

    except ImportError as e:
        error_msg = f"Import Error: {e}\n\nPlease make sure all required modules are in the same directory."
        print(f"❌ {error_msg}")

        # Try to show GUI error if tkinter is available
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Import Error", error_msg)
        except:
            pass

        input("Press Enter to exit...")

    except Exception as e:
        error_msg = f"Application Error: {e}"
        print(f"❌ {error_msg}")

        # Try to show GUI error if tkinter is available
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Application Error", error_msg)
        except:
            pass

        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
